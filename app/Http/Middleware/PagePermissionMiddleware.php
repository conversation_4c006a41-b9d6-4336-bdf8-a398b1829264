<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\PagePermission;

class PagePermissionMiddleware
{
    /**
     * Routes that should be accessible without authentication
     */
    protected $publicRoutes = [
        'home',
        'login',
        'register',
        'password.request',
        'password.reset',
        'password.email',
        'password.store',
        'otp.request',
        'otp.store',
        'otp.verify',
        'otp.confirm',
        'admin.setup',
        'admin.setup.store',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $routeName = $request->route()->getName();

        // Allow access to public routes without authentication
        if (in_array($routeName, $this->publicRoutes)) {
            return $next($request);
        }

        // For protected routes, require authentication
        if (!$request->user()) {
            abort(401, 'Unauthorized.');
        }

        $user = $request->user();

        // Find page permission for this route
        $pagePermission = PagePermission::where('page_route', $routeName)
            ->where('is_active', true)
            ->first();

        if (!$pagePermission) {
            // If no specific permission is set for this page, allow access
            return $next($request);
        }

        // Check role-based access first (takes precedence)
        if (!empty($pagePermission->required_roles)) {
            if (!$pagePermission->hasRequiredRoles($user->role->slug)) {
                abort(403, 'Access denied. You do not have the required role to access this page.');
            }
        }

        // Check permission-based access
        if (!empty($pagePermission->required_permissions)) {
            $userPermissions = $user->getUserPermissions();
            if (!$pagePermission->hasRequiredPermissions($userPermissions)) {
                abort(403, 'Access denied. You do not have the required permissions to access this page.');
            }
        }

        return $next($request);
    }
}
